import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../flutter_flow/flutter_flow_theme.dart';
import '../../flutter_flow/flutter_flow_util.dart';
import '../../providers/app_provider.dart';

class DeviceConfigTab extends StatelessWidget {
  const DeviceConfigTab({Key? key}) : super(key: key);

  /// Formats device number to show only last 6 digits with asterisks for previous digits
  String _formatDeviceNumber(String deviceNumber) {
    if (deviceNumber.isEmpty) return 'N/A';

    if (deviceNumber.length <= 6) {
      return deviceNumber;
    }

    // Show asterisks for all but last 6 digits
    final hiddenLength = deviceNumber.length - 6;
    final asterisks = '*' * hiddenLength;
    final lastSixDigits = deviceNumber.substring(deviceNumber.length - 6);

    return '$asterisks$lastSixDigits';
  }

  /// Formats renter field - shows "Pool Broker: Байхгүй" if empty, otherwise shows the renter domain name
  String _formatRenterInfo(String renter) {
    if (renter.isEmpty) {
      return 'Pool Broker: Байхгүй';
    }
    return 'Pool Broker: $renter';
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AppProvider>(
      builder: (context, appProvider, child) {
        final user = appProvider.authClient;
        final deviceNumber = user?.device?.deviceNumber ?? '';
        final renter = user?.device?.renter ?? '';
        final formattedDeviceNumber = _formatDeviceNumber(deviceNumber);
        final formattedRenterInfo = _formatRenterInfo(renter);

        return Padding(
          padding: EdgeInsetsDirectional.fromSTEB(0, 24, 0, 0),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.max,
              children: [
                // Device Number Display at the top
                Padding(
                  padding: EdgeInsetsDirectional.fromSTEB(24, 12, 24, 0),
                  child: Container(
                    width: double.infinity,
                    padding: EdgeInsetsDirectional.fromSTEB(16, 12, 16, 12),
                    decoration: BoxDecoration(
                      color: FlutterFlowTheme.of(context).secondaryBackground,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: FlutterFlowTheme.of(context).alternate,
                        width: 1,
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Device Number',
                          style: FlutterFlowTheme.of(context)
                              .bodyText2
                              .override(
                                fontFamily: 'Roboto',
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                                color:
                                    FlutterFlowTheme.of(context).secondaryText,
                              ),
                        ),
                        SizedBox(height: 4),
                        Text(
                          formattedDeviceNumber,
                          style: FlutterFlowTheme.of(context).title3.override(
                                fontFamily: 'Roboto',
                                fontSize: 18,
                                fontWeight: FontWeight.w600,
                                letterSpacing: 1.2,
                              ),
                        ),
                        SizedBox(height: 12),
                        Text(
                          formattedRenterInfo,
                          style: FlutterFlowTheme.of(context)
                              .bodyText1
                              .override(
                                fontFamily: 'Roboto',
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                                color: FlutterFlowTheme.of(context).primaryText,
                              ),
                        ),
                      ],
                    ),
                  ),
                ),

                // Title
                Padding(
                  padding: EdgeInsetsDirectional.fromSTEB(24, 24, 24, 0),
                  child: Text(
                    FFLocalizations.of(context).getText(
                      'device_config_placeholder' /* Device Configuration */,
                    ),
                    style: FlutterFlowTheme.of(context).title2,
                  ),
                ),

                // Placeholder content
                Padding(
                  padding: EdgeInsetsDirectional.fromSTEB(24, 12, 24, 0),
                  child: Text(
                    'Device configuration features will be implemented here.',
                    style: FlutterFlowTheme.of(context).bodyText1,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
